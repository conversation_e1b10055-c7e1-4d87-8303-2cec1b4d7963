<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.gaogle.dao.master.RegisterPublishAnnouncementMapper">
    <resultMap type="top.gaogle.pojo.model.RegisterPublishAnnouncementModel" id="RegisterPublishAnnouncementMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="registerPublishId" column="register_publish_id" jdbcType="VARCHAR"/>
        <result property="enterpriseId" column="enterprise_id" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createAt" column="create_at" jdbcType="INTEGER"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateAt" column="update_at" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="sqlAllColumns">
        id, title, content, type, register_publish_id, enterprise_id, create_by, create_at, update_by, update_at
    </sql>
    <insert id="insert">
        insert into register_publish_announcement(<include refid="sqlAllColumns"/>)
        values (#{id},#{title}, #{content}, #{type}, #{registerPublishId}, #{enterpriseId}, #{createBy}, #{createAt},
        #{updateBy}, #{updateAt})
    </insert>
    <!--通过主键修改数据-->
    <update id="update">
        update register_publish_announcement
        <set>
            <if test="title != null and title != ''">
                title = #{title},
            </if>
            <if test="content != null and content != ''">
                content = #{content},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="registerPublishId != null and registerPublishId != ''">
                register_publish_id = #{registerPublishId},
            </if>
            <if test="enterpriseId != null and enterpriseId != ''">
                enterprise_id = #{enterpriseId},
            </if>
            <if test="createBy != null and createBy != ''">
                create_by = #{createBy},
            </if>
            <if test="createAt != null">
                create_at = #{createAt},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="updateAt != null">
                update_at = #{updateAt},
            </if>
        </set>
        where id = #{id}
    </update>
    <update id="updateByIdAndEnterpriseId">
        update register_publish_announcement
        <set>
            <if test="title != null and title != ''">
                title = #{title},
            </if>
            <if test="content != null and content != ''">
                content = #{content},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="registerPublishId != null and registerPublishId != ''">
                register_publish_id = #{registerPublishId},
            </if>
            <if test="enterpriseId != null and enterpriseId != ''">
                enterprise_id = #{enterpriseId},
            </if>
            <if test="createBy != null and createBy != ''">
                create_by = #{createBy},
            </if>
            <if test="createAt != null">
                create_at = #{createAt},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="updateAt != null">
                update_at = #{updateAt},
            </if>
        </set>
        where id = #{id} and enterprise_id = #{enterpriseId}
    </update>
    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from register_publish_announcement
        where id = #{id}
    </delete>
    <delete id="deleteByIdAndEnterpriseId">
        delete
        from register_publish_announcement
        where id = #{id}
          and enterprise_id = #{enterpriseId}
    </delete>
    <select id="queryByPageAndCondition" resultMap="RegisterPublishAnnouncementMap">
        select
        <include refid="sqlAllColumns"/>
        from register_publish_announcement
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="title != null and title != ''">
                and title = #{title}
            </if>
            <if test="content != null and content != ''">
                and content = #{content}
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="registerPublishId != null and registerPublishId != ''">
                and register_publish_id = #{registerPublishId}
            </if>
            <if test="enterpriseId != null and enterpriseId != ''">
                and enterprise_id = #{enterpriseId}
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy}
            </if>
            <if test="createAt != null">
                and create_at = #{createAt}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy}
            </if>
            <if test="updateAt != null">
                and update_at = #{updateAt}
            </if>
        </where>
        ORDER BY
        <choose>
            <when test='sort == null or sort.trim == ""'>
                create_at DESC
            </when>
            <otherwise>
                `${sort}`
                <choose>
                    <when test='order != null and "DESC".equalsIgnoreCase(order.trim())'>
                        DESC
                    </when>
                    <otherwise>
                        ASC
                    </otherwise>
                </choose>
            </otherwise>
        </choose>
    </select>
    <select id="queryOneByIdAndEnterpriseId"
            resultMap="RegisterPublishAnnouncementMap">

    </select>
    <select id="queryOneById" resultMap="RegisterPublishAnnouncementMap">
        select
        <include refid="sqlAllColumns"/>
        from register_publish_announcement
        where id = #{id}
    </select>


</mapper>