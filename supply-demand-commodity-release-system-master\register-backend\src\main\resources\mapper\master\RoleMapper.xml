<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.gaogle.dao.master.RoleMapper">
    <resultMap type="top.gaogle.pojo.model.RoleModel" id="RoleMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createAt" column="create_at" jdbcType="INTEGER"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateAt" column="update_at" jdbcType="INTEGER"/>
        <result property="delFlag" column="del_flag" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="sqlAllColumns">
        id, name, description, type, create_by, create_at, update_by, update_at, del_flag
    </sql>

    <sql id="sqlExtraColumns">
        r.id, r.name, r.description, r.type, r.create_by, r.create_at, r.update_by, r.update_at, r.del_flag,ur.user_id
    </sql>

    <insert id="insert">
        insert into role(<include refid="sqlAllColumns"/>)
        values (#{id}, #{name}, #{description}, #{type}, #{createBy}, #{createAt}, #{updateBy}, #{updateAt}, #{delFlag})
    </insert>
    <update id="patchRole">
        update role
        <set>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="description != null and description != ''">
                description = #{description},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="createBy != null and createBy != ''">
                create_by = #{createBy},
            </if>
            <if test="createAt != null">
                create_at = #{createAt},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="updateAt != null">
                update_at = #{updateAt},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
        </set>
        where id = #{id}
    </update>
    <delete id="deleteRole">
        delete
        from role
        where id = #{roleId}
    </delete>


    <select id="getAllRoleList" resultMap="RoleMap">
        select
        <include refid="sqlAllColumns"/>
        from role
    </select>

    <select id="queryByPageAndCondition" resultMap="RoleMap">
        select
        <include refid="sqlAllColumns"/>
        from role
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="search != null and search != ''">
                and name like concat('%', #{search}, '%')
            </if>
            <if test="description != null and description != ''">
                and description = #{description}
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy}
            </if>
            <if test="createAt != null">
                and create_at = #{createAt}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy}
            </if>
            <if test="updateAt != null">
                and update_at = #{updateAt}
            </if>
            <if test="delFlag != null">
                and del_flag = #{delFlag}
            </if>
        </where>
        ORDER BY
        <choose>
            <when test='sort == null or sort.trim == ""'>
                create_at DESC
            </when>
            <otherwise>
                `${sort}`
                <choose>
                    <when test='order != null and "DESC".equalsIgnoreCase(order.trim())'>
                        DESC
                    </when>
                    <otherwise>
                        ASC
                    </otherwise>
                </choose>
            </otherwise>
        </choose>
    </select>
    <select id="querySizeByName" resultType="java.lang.Integer">
        SELECT count(*)
        from role
        where name = #{name}
    </select>
    <select id="querySizeExcludeSelfByName" resultType="java.lang.Integer">
        SELECT count(*)
        from role
        where name = #{name}
          and id != #{id}
    </select>
    <select id="queryAll" resultMap="RoleMap">
        select
        <include refid="sqlAllColumns"/>
        from role
    </select>
    <select id="queryDetailByRoleId" resultMap="RoleMap">
        select
        <include refid="sqlAllColumns"/>
        from role where id =#{roleId}
    </select>
    <select id="queryRoleByUserId" resultMap="RoleMap">
        select
        <include refid="sqlExtraColumns"/>
        from role r
        left join user_role ur on ur.role_id = r.id
        where ur.user_id =#{userId}
    </select>
    <select id="queryRolesByUserIds" resultMap="RoleMap">
        select
        <include refid="sqlExtraColumns"/>
        from role r
        left join user_role ur on ur.role_id = r.id
        where ur.user_id IN
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


</mapper>