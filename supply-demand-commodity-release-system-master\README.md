#  基于Spring Boot-Vue的农产品销售-供销-供需发布系统-代码设计与实现-前后端分离一键部署版

### 软件技术架构
1. 后端: Spring Boot2.5.3、Mybatis
2. 前端：Vue2.0
3. 数据库：Mysql、Redis
4. 部署: Docker、Docker-compose

#### 演示网址

- 用户端：[www.enrollpro.top](http://www.enrollpro.top)
- 企业端：[enterprise.enrollpro.top](http://enterprise.enrollpro.top)
- 管理端：[admin.enrollpro.top](http://admin.enrollpro.top)


账号：<EMAIL> 密码：Hh123@qqcom

### 在线展示

1. [ **演示视频 :tw-1f60d:**  ](https://www.bilibili.com/video/BV16r42177r8/?spm_id_from=333.999.0.0&vd_source=eac6949bd2385c66c0a975d5765c99a5)
2. [  :tw-25b6: **系统使用演示视频** ](https://www.bilibili.com/video/BV16r42177r8/?spm_id_from=333.999.0.0&vd_source=eac6949bd2385c66c0a975d5765c99a5)

### 简介
农产品销售系统，农产品供需关系发布系统，求购农产品，发布农产品信息，网上销售购物农产品系统

### 功能介绍


1. 农产品信息发布，商品发布，农产品售卖。
1. 发布求购需求，发布自己需要的商品信息。
1. 购物车，自己购买的商品可以加入购物车.
1. 引入支付宝在线支付系统。
1. 商家可以查看购买的订单，进行发货。
1. 农业知识模块，进行知识学习。
1. 用户管理，用户角色分为普通用户，专家学者，管理员。

    

### 使用说明

- 本项目可以自行拉取代码，项目启动运行若有疑问可以联系作者咨询
- 本代码存在许多不足，欢迎大佬提交代码合并请求

- :tw-1f234: **若有侵权信息请联系作者删除，您的star是我们前进的动力** 
- :tw-1f46c: **作者🐧：3300755918** 


### 功能详情图

[  :tw-25b6: **系统使用演示视频** ](https://www.bilibili.com/video/BV16r42177r8/?spm_id_from=333.999.0.0&vd_source=eac6949bd2385c66c0a975d5765c99a5)

![输入图片说明](images/微信图片_20240512224322.png)
![输入图片说明](images/微信图片_20240512224353.png)
![输入图片说明](images/微信图片_20240512224413.png)
![输入图片说明](images/微信图片_20240512224500.png)
![输入图片说明](images/微信图片_20240512224612.png)
![输入图片说明](images/微信图片_20240512224722.png)
![输入图片说明](images/微信图片_20240512224739.png)
![输入图片说明](images/微信图片_20240512224750.png)
![输入图片说明](images/微信图片_20240512224802.png)
![输入图片说明](images/微信图片_20240512224816.png)
![输入图片说明](images/微信图片_20240512224816.png)
![输入图片说明](images/微信图片_20240512224827.png)
![输入图片说明](images/微信图片_20240512224854.png)

[  :tw-25b6: **系统使用演示视频** ](https://www.bilibili.com/video/BV16r42177r8/?spm_id_from=333.999.0.0&vd_source=eac6949bd2385c66c0a975d5765c99a5)
