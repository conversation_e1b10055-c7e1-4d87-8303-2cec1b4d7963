<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.gaogle.dao.master.RegisterBillMapper">

    <resultMap type="top.gaogle.pojo.model.RegisterBillModel" id="RegisterBillMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="registerPublishId" column="register_publish_id" jdbcType="VARCHAR"/>
        <result property="enterpriseId" column="enterprise_id" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="alipayTradeStatus" column="alipay_trade_status" jdbcType="VARCHAR"/>
        <result property="alipayTradeNo" column="alipay_trade_no" jdbcType="VARCHAR"/>
        <result property="amount" column="amount" jdbcType="INTEGER"/>
        <result property="alipayTimeExpire" column="alipay_time_expire" jdbcType="VARCHAR"/>
        <result property="alipayTimeExpireAt" column="alipay_time_expire_at" jdbcType="INTEGER"/>
        <result property="completionAt" column="completion_at" jdbcType="INTEGER"/>
        <result property="subject" column="subject" jdbcType="VARCHAR"/>
        <result property="comment" column="comment" jdbcType="VARCHAR"/>
        <result property="systemComment" column="system_comment" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createAt" column="create_at" jdbcType="INTEGER"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateAt" column="update_at" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="sqlAllColumns">
        id, register_publish_id, enterprise_id, status, alipay_trade_status, alipay_trade_no, amount, alipay_time_expire, alipay_time_expire_at, completion_at, subject, comment, system_comment, create_by, create_at, update_by, update_at
    </sql>

    <insert id="insert">
        insert into register_bill(<include refid="sqlAllColumns"/>)
        values (#{id},#{registerPublishId}, #{enterpriseId}, #{status}, #{alipayTradeStatus}, #{alipayTradeNo},
        #{amount}, #{alipayTimeExpire}, #{alipayTimeExpireAt}, #{completionAt}, #{subject}, #{comment},
        #{systemComment}, #{createBy}, #{createAt}, #{updateBy}, #{updateAt})
    </insert>

    <update id="updateStatusByBillId">
        UPDATE register_bill
        SET status             = #{status},
            alipay_trade_status=#{tradeStatus},
            alipay_trade_no=#{tradeNo},
            completion_at=#{completionAt}
        WHERE (status != #{status} OR status IS NULL)
          AND id = #{id};
    </update>

    <select id="queryOneById" resultMap="RegisterBillMap">
        select
        <include refid="sqlAllColumns"/>
        from register_bill
        where id = #{id}
    </select>

</mapper>