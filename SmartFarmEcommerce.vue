<template>
  <div id="app">
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="nav-container">
        <div class="nav-brand">
          <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIwIDVMMzUgMTVWMzVMMjAgNDBMNSAzNVYxNUwyMCA1WiIgZmlsbD0iIzRDQUY1MCIvPgo8cGF0aCBkPSJNMjAgMTBMMzAgMTZWMzBMMjAgMzVMMTAgMzBWMTZMMjAgMTBaIiBmaWxsPSIjODFDNzg0Ii8+CjxjaXJjbGUgY3g9IjIwIiBjeT0iMjAiIHI9IjUiIGZpbGw9IiNGRkY4RTEiLz4KPC9zdmc+" alt="智农商城" class="logo">
          <span class="brand-text">智农商城</span>
        </div>
        <ul class="nav-menu" :class="{ active: isMenuOpen }">
          <li><a href="#home" @click="setActiveSection('home')">首页</a></li>
          <li><a href="#products" @click="setActiveSection('products')">农产品</a></li>
          <li><a href="#farmers" @click="setActiveSection('farmers')">农户入驻</a></li>
          <li><a href="#about" @click="setActiveSection('about')">关于我们</a></li>
          <li><a href="#contact" @click="setActiveSection('contact')">联系我们</a></li>
        </ul>
        <div class="nav-actions">
          <button class="cart-btn">
            <span class="cart-icon">🛒</span>
            <span class="cart-count">{{ cartCount }}</span>
          </button>
          <button class="login-btn">登录</button>
        </div>
        <div class="hamburger" @click="toggleMenu">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </nav>

    <!-- 主要内容 -->
    <main>
      <!-- 首页轮播 -->
      <section id="home" class="hero-section" v-show="activeSection === 'home'">
        <div class="hero-slider">
          <div class="hero-slide active">
            <div class="hero-content">
              <h1>智能助农，品质生活</h1>
              <p>连接田间地头与餐桌，为您提供最新鲜的农产品</p>
              <button class="cta-button" @click="setActiveSection('products')">立即购买</button>
            </div>
            <div class="hero-image">
              <div class="image-placeholder">🌾</div>
            </div>
          </div>
        </div>
        
        <!-- 特色服务 -->
        <div class="features">
          <div class="feature-card">
            <div class="feature-icon">🚚</div>
            <h3>快速配送</h3>
            <p>24小时内新鲜直达</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🌱</div>
            <h3>绿色有机</h3>
            <p>严格品质把控</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">👨‍🌾</div>
            <h3>直供农户</h3>
            <p>减少中间环节</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">💰</div>
            <h3>价格实惠</h3>
            <p>产地直销价格</p>
          </div>
        </div>
      </section>

      <!-- 农产品展示 -->
      <section id="products" class="products-section" v-show="activeSection === 'products'">
        <div class="container">
          <h2 class="section-title">精选农产品</h2>
          <div class="product-categories">
            <button 
              v-for="category in categories" 
              :key="category"
              :class="{ active: selectedCategory === category }"
              @click="selectedCategory = category"
              class="category-btn"
            >
              {{ category }}
            </button>
          </div>
          <div class="products-grid">
            <div 
              v-for="product in filteredProducts" 
              :key="product.id"
              class="product-card"
              @click="selectProduct(product)"
            >
              <div class="product-image">{{ product.emoji }}</div>
              <div class="product-info">
                <h3>{{ product.name }}</h3>
                <p class="product-origin">{{ product.origin }}</p>
                <div class="product-price">
                  <span class="current-price">¥{{ product.price }}</span>
                  <span class="original-price">¥{{ product.originalPrice }}</span>
                </div>
                <button class="add-to-cart" @click.stop="addToCart(product)">
                  加入购物车
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 农户入驻 -->
      <section id="farmers" class="farmers-section" v-show="activeSection === 'farmers'">
        <div class="container">
          <h2 class="section-title">农户入驻</h2>
          <div class="farmers-content">
            <div class="farmers-benefits">
              <h3>加入我们的优势</h3>
              <ul>
                <li>🎯 精准营销，直达消费者</li>
                <li>📱 智能管理系统，轻松运营</li>
                <li>💡 数据分析，优化种植</li>
                <li>🤝 专业团队支持</li>
              </ul>
            </div>
            <div class="join-form">
              <h3>立即申请入驻</h3>
              <form @submit.prevent="submitApplication">
                <input v-model="application.name" type="text" placeholder="农户姓名" required>
                <input v-model="application.phone" type="tel" placeholder="联系电话" required>
                <input v-model="application.location" type="text" placeholder="农场地址" required>
                <textarea v-model="application.products" placeholder="主要农产品" required></textarea>
                <button type="submit" class="submit-btn">提交申请</button>
              </form>
            </div>
          </div>
        </div>
      </section>

      <!-- 关于我们 -->
      <section id="about" class="about-section" v-show="activeSection === 'about'">
        <div class="container">
          <h2 class="section-title">关于智农商城</h2>
          <div class="about-content">
            <div class="about-text">
              <p>智农商城致力于打造中国领先的智能助农电商平台，通过科技创新连接农民与消费者，推动农业现代化发展。</p>
              <div class="stats">
                <div class="stat-item">
                  <div class="stat-number">10000+</div>
                  <div class="stat-label">合作农户</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">50万+</div>
                  <div class="stat-label">用户信赖</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">100+</div>
                  <div class="stat-label">城市覆盖</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 联系我们 -->
      <section id="contact" class="contact-section" v-show="activeSection === 'contact'">
        <div class="container">
          <h2 class="section-title">联系我们</h2>
          <div class="contact-content">
            <div class="contact-info">
              <div class="contact-item">
                <span class="contact-icon">📍</span>
                <div>
                  <h4>地址</h4>
                  <p>北京市朝阳区智农大厦</p>
                </div>
              </div>
              <div class="contact-item">
                <span class="contact-icon">📞</span>
                <div>
                  <h4>电话</h4>
                  <p>400-888-9999</p>
                </div>
              </div>
              <div class="contact-item">
                <span class="contact-icon">✉️</span>
                <div>
                  <h4>邮箱</h4>
                  <p><EMAIL></p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h4>智农商城</h4>
            <p>智能助农，品质生活</p>
          </div>
          <div class="footer-section">
            <h4>快速链接</h4>
            <ul>
              <li><a href="#home">首页</a></li>
              <li><a href="#products">农产品</a></li>
              <li><a href="#farmers">农户入驻</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>客户服务</h4>
            <ul>
              <li><a href="#contact">联系我们</a></li>
              <li><a href="#">帮助中心</a></li>
              <li><a href="#">售后服务</a></li>
            </ul>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2024 智农商城. 保留所有权利.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script>
export default {
  name: 'SmartFarmEcommerce',
  data() {
    return {
      activeSection: 'home',
      isMenuOpen: false,
      cartCount: 0,
      selectedCategory: '全部',
      categories: ['全部', '蔬菜', '水果', '粮食', '特产'],
      products: [
        { id: 1, name: '有机西红柿', category: '蔬菜', price: 15, originalPrice: 20, origin: '山东寿光', emoji: '🍅' },
        { id: 2, name: '新鲜苹果', category: '水果', price: 12, originalPrice: 18, origin: '陕西洛川', emoji: '🍎' },
        { id: 3, name: '优质大米', category: '粮食', price: 25, originalPrice: 30, origin: '东北五常', emoji: '🌾' },
        { id: 4, name: '农家土鸡蛋', category: '特产', price: 35, originalPrice: 45, origin: '河北承德', emoji: '🥚' },
        { id: 5, name: '绿色黄瓜', category: '蔬菜', price: 8, originalPrice: 12, origin: '河南商丘', emoji: '🥒' },
        { id: 6, name: '香甜橙子', category: '水果', price: 18, originalPrice: 25, origin: '湖南永州', emoji: '🍊' }
      ],
      application: {
        name: '',
        phone: '',
        location: '',
        products: ''
      }
    }
  },
  computed: {
    filteredProducts() {
      if (this.selectedCategory === '全部') {
        return this.products;
      }
      return this.products.filter(product => product.category === this.selectedCategory);
    }
  },
  methods: {
    setActiveSection(section) {
      this.activeSection = section;
      this.isMenuOpen = false;
    },
    toggleMenu() {
      this.isMenuOpen = !this.isMenuOpen;
    },
    addToCart(product) {
      this.cartCount++;
      // 这里可以添加更多购物车逻辑
    },
    selectProduct(product) {
      // 产品详情逻辑
      console.log('选择产品:', product);
    },
    submitApplication() {
      // 提交申请逻辑
      alert('申请已提交，我们会尽快联系您！');
      this.application = { name: '', phone: '', location: '', products: '' };
    }
  }
}
</script>

<style scoped>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 导航栏样式 */
.navbar {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1000;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 20px;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 10px;
}

.logo {
  width: 40px;
  height: 40px;
}

.brand-text {
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-menu a {
  color: white;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s;
}

.nav-menu a:hover {
  color: #FFE082;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.cart-btn {
  background: rgba(255,255,255,0.2);
  border: none;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: background 0.3s;
}

.cart-btn:hover {
  background: rgba(255,255,255,0.3);
}

.cart-count {
  background: #FF5722;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
}

.login-btn {
  background: white;
  color: #4CAF50;
  border: none;
  padding: 0.5rem 1.5rem;
  border-radius: 20px;
  cursor: pointer;
  font-weight: 500;
  transition: transform 0.3s;
}

.login-btn:hover {
  transform: translateY(-2px);
}

.hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
}

.hamburger span {
  width: 25px;
  height: 3px;
  background: white;
  margin: 3px 0;
  transition: 0.3s;
}

/* 主要内容样式 */
main {
  margin-top: 80px;
}

/* 首页轮播样式 */
.hero-section {
  background: linear-gradient(135deg, #E8F5E8, #F1F8E9);
  padding: 4rem 0;
}

.hero-slider {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.hero-slide {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: center;
}

.hero-content h1 {
  font-size: 3rem;
  color: #2E7D32;
  margin-bottom: 1rem;
  font-weight: bold;
}

.hero-content p {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 2rem;
}

.cta-button {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  border-radius: 30px;
  cursor: pointer;
  transition: transform 0.3s, box-shadow 0.3s;
}

.cta-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(76, 175, 80, 0.3);
}

.hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-placeholder {
  font-size: 8rem;
  background: linear-gradient(135deg, #81C784, #66BB6A);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

/* 特色服务样式 */
.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 4rem auto 0;
  padding: 0 20px;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  transition: transform 0.3s, box-shadow 0.3s;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.feature-card h3 {
  color: #2E7D32;
  margin-bottom: 0.5rem;
}

/* 产品展示样式 */
.products-section {
  padding: 4rem 0;
  background: #FAFAFA;
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  color: #2E7D32;
  margin-bottom: 3rem;
}

.product-categories {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.category-btn {
  background: white;
  border: 2px solid #4CAF50;
  color: #4CAF50;
  padding: 0.5rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s;
}

.category-btn.active,
.category-btn:hover {
  background: #4CAF50;
  color: white;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.product-card {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  transition: transform 0.3s, box-shadow 0.3s;
  cursor: pointer;
}

.product-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.product-image {
  height: 200px;
  background: linear-gradient(135deg, #E8F5E8, #F1F8E9);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 4rem;
}

.product-info {
  padding: 1.5rem;
}

.product-info h3 {
  color: #2E7D32;
  margin-bottom: 0.5rem;
}

.product-origin {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.current-price {
  font-size: 1.5rem;
  font-weight: bold;
  color: #FF5722;
}

.original-price {
  text-decoration: line-through;
  color: #999;
}

.add-to-cart {
  width: 100%;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  border: none;
  padding: 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: transform 0.3s;
}

.add-to-cart:hover {
  transform: translateY(-2px);
}

/* 农户入驻样式 */
.farmers-section {
  padding: 4rem 0;
  background: linear-gradient(135deg, #E8F5E8, #F1F8E9);
}

.farmers-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

.farmers-benefits h3 {
  color: #2E7D32;
  font-size: 1.8rem;
  margin-bottom: 2rem;
}

.farmers-benefits ul {
  list-style: none;
}

.farmers-benefits li {
  padding: 1rem 0;
  font-size: 1.1rem;
  border-bottom: 1px solid rgba(46, 125, 50, 0.1);
}

.join-form {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.join-form h3 {
  color: #2E7D32;
  margin-bottom: 2rem;
  text-align: center;
}

.join-form input,
.join-form textarea {
  width: 100%;
  padding: 1rem;
  margin-bottom: 1rem;
  border: 2px solid #E0E0E0;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s;
}

.join-form input:focus,
.join-form textarea:focus {
  outline: none;
  border-color: #4CAF50;
}

.join-form textarea {
  height: 100px;
  resize: vertical;
}

.submit-btn {
  width: 100%;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  border: none;
  padding: 1rem;
  border-radius: 8px;
  font-size: 1.1rem;
  cursor: pointer;
  transition: transform 0.3s;
}

.submit-btn:hover {
  transform: translateY(-2px);
}

/* 关于我们样式 */
.about-section {
  padding: 4rem 0;
  background: white;
}

.about-content {
  text-align: center;
}

.about-text p {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 3rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 3rem;
  font-weight: bold;
  color: #4CAF50;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 1.1rem;
  color: #666;
}

/* 联系我们样式 */
.contact-section {
  padding: 4rem 0;
  background: #FAFAFA;
}

.contact-content {
  max-width: 600px;
  margin: 0 auto;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.contact-icon {
  font-size: 2rem;
  width: 60px;
  text-align: center;
}

.contact-item h4 {
  color: #2E7D32;
  margin-bottom: 0.5rem;
}

/* 页脚样式 */
.footer {
  background: #2E7D32;
  color: white;
  padding: 3rem 0 1rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h4 {
  margin-bottom: 1rem;
  color: #81C784;
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 0.5rem;
}

.footer-section a {
  color: #CCCCCC;
  text-decoration: none;
  transition: color 0.3s;
}

.footer-section a:hover {
  color: white;
}

.footer-bottom {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid #4CAF50;
  color: #CCCCCC;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hamburger {
    display: flex;
  }

  .nav-menu {
    position: fixed;
    left: -100%;
    top: 80px;
    flex-direction: column;
    background: #4CAF50;
    width: 100%;
    text-align: center;
    transition: 0.3s;
    padding: 2rem 0;
  }

  .nav-menu.active {
    left: 0;
  }

  .hero-slide {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .hero-content h1 {
    font-size: 2rem;
  }

  .features {
    grid-template-columns: 1fr;
  }

  .farmers-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .product-categories {
    justify-content: flex-start;
    overflow-x: auto;
    padding-bottom: 1rem;
  }

  .stats {
    grid-template-columns: 1fr;
  }

  .contact-item {
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 15px;
  }

  .hero-content h1 {
    font-size: 1.8rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .products-grid {
    grid-template-columns: 1fr;
  }
}
</style>
