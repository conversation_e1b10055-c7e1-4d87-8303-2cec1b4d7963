# 本地运行后端服务使用的配置文件
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************************************************************************
    username: root
    password: Mysql123
  redis:
    host: localhost
    port: 6379
    password: Redis123
    database: 0

server:
  port: 6666

mybatis:
  mapper-locations: classpath:mapper/*.xml

gaogle:
  author: 高歌 曹浩