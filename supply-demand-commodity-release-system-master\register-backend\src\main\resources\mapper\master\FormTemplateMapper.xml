<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.gaogle.dao.master.FormTemplateMapper">
    <resultMap type="top.gaogle.pojo.model.FormTemplateModel" id="FormTemplateMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="enterpriseId" column="enterprise_id" jdbcType="VARCHAR"/>
        <result property="range" column="range" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createAt" column="create_at" jdbcType="INTEGER"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateAt" column="update_at" jdbcType="INTEGER"/>
        <result property="flag" column="flag" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="sqlAllColumns">
        id, title, description, content, enterprise_id, `range`, `status`, create_by, create_at, update_by, update_at,flag
    </sql>
    <insert id="insert">
        insert into form_template(<include refid="sqlAllColumns"/>)
        values (#{id},#{title}, #{description}, #{content}, #{enterpriseId}, #{range}, #{status}, #{createBy},
        #{createAt}, #{updateBy}, #{updateAt},#{flag})
    </insert>
    <update id="update">
        update form_template
        <set>
            <if test="title != null and title != ''">
                title = #{title},
            </if>
            <if test="description != null and description != ''">
                description = #{description},
            </if>
            <if test="content != null and content != ''">
                content = #{content},
            </if>
            <if test="enterpriseId != null and enterpriseId != ''">
                enterprise_id = #{enterpriseId},
            </if>
            <if test="range != null">
                range = #{range},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="createBy != null and createBy != ''">
                create_by = #{createBy},
            </if>
            <if test="createAt != null">
                create_at = #{createAt},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="updateAt != null">
                update_at = #{updateAt},
            </if>
        </set>
        where id = #{id}
    </update>
    <delete id="deleteById">
        delete
        from form_template
        where id = #{id}
    </delete>
    <select id="queryByPageAndCondition" resultMap="FormTemplateMap">
        select
        <include refid="sqlAllColumns"/>
        from form_template
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="title != null and title != ''">
                and title = #{title}
            </if>
            <if test="description != null and description != ''">
                and description = #{description}
            </if>
            <if test="content != null and content != ''">
                and content = #{content}
            </if>
            <if test="enterpriseId != null and enterpriseId != ''">
                and enterprise_id = #{enterpriseId}
            </if>
            <if test="range != null">
                and range = #{range}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy}
            </if>
            <if test="createAt != null">
                and create_at = #{createAt}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy}
            </if>
            <if test="updateAt != null">
                and update_at = #{updateAt}
            </if>
        </where>
        ORDER BY
        <choose>
            <when test='sort == null or sort.trim == ""'>
                create_at DESC
            </when>
            <otherwise>
                `${sort}`
                <choose>
                    <when test='order != null and "DESC".equalsIgnoreCase(order.trim())'>
                        DESC
                    </when>
                    <otherwise>
                        ASC
                    </otherwise>
                </choose>
            </otherwise>
        </choose>
    </select>
    <select id="queryOneById" resultMap="FormTemplateMap">
        select
        <include refid="sqlAllColumns"/>
        from form_template
        where id = #{id}
    </select>
    <select id="queryOneByFlag" resultMap="FormTemplateMap">
        select
        <include refid="sqlAllColumns"/>
        from form_template
        where flag = #{flag}
    </select>


</mapper>