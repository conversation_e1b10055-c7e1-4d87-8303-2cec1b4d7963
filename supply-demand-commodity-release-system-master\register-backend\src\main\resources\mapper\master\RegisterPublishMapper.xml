<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.gaogle.dao.master.RegisterPublishMapper">
    <resultMap type="top.gaogle.pojo.model.RegisterPublishModel" id="RegisterPublishMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="publishStatus" column="publish_status" jdbcType="INTEGER"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="enterpriseId" column="enterprise_id" jdbcType="VARCHAR"/>
        <result property="templateFlag" column="template_flag" jdbcType="INTEGER"/>
        <result property="templateCopy" column="template_copy" jdbcType="VARCHAR"/>
        <result property="startAt" column="start_at" jdbcType="INTEGER"/>
        <result property="endAt" column="end_at" jdbcType="INTEGER"/>
        <result property="activityFlag" column="activity_flag" jdbcType="VARCHAR"/>
        <result property="activityStartAt" column="activity_start_at" jdbcType="INTEGER"/>
        <result property="activityEndAt" column="activity_end_at" jdbcType="INTEGER"/>
        <result property="ticketFlag" column="ticket_flag" jdbcType="VARCHAR"/>
        <result property="ticketStartAt" column="ticket_start_at" jdbcType="INTEGER"/>
        <result property="ticketEndAt" column="ticket_end_at" jdbcType="INTEGER"/>
        <result property="ticketAttach" column="ticket_attach" jdbcType="VARCHAR"/>
        <result property="payFlag" column="pay_flag" jdbcType="VARCHAR"/>
        <result property="cost" column="cost" jdbcType="INTEGER"/>
        <result property="scoreFlag" column="score_flag" jdbcType="VARCHAR"/>
        <result property="scoreStartAt" column="score_start_at" jdbcType="INTEGER"/>
        <result property="scoreEndAt" column="score_end_at" jdbcType="INTEGER"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createAt" column="create_at" jdbcType="INTEGER"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateAt" column="update_at" jdbcType="INTEGER"/>
        <result property="delFlag" column="del_flag" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="sqlAllColumns">
        id, title, status,publish_status, description, pay_flag,cost ,start_at, end_at, activity_flag, activity_start_at, activity_end_at,ticket_flag, ticket_start_at, ticket_end_at, ticket_attach, score_flag, score_start_at, score_end_at, template_flag, template_copy, enterprise_id, create_by, create_at, update_by, update_at
    </sql>
    <insert id="insert">
        insert into register_publish(<include refid="sqlAllColumns"/>)
        values (#{id},#{title}, #{status},#{publishStatus}, #{description}, #{payFlag},#{cost},#{startAt}, #{endAt},
        #{activityFlag},#{activityStartAt},#{activityEndAt},#{ticketFlag}, #{ticketStartAt},
        #{ticketEndAt}, #{ticketAttach}, #{scoreFlag}, #{scoreStartAt}, #{scoreEndAt}, #{templateFlag}, #{templateCopy},
        #{enterpriseId}, #{createBy}, #{createAt}, #{updateBy}, #{updateAt})
    </insert>
    <update id="update">
        update register_publish
        <set>
            <if test="title != null and title != ''">
                title = #{title},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="description != null and description != ''">
                description = #{description},
            </if>
            <if test="startAt != null">
                start_at = #{startAt},
            </if>
            <if test="endAt != null">
                end_at = #{endAt},
            </if>
            <if test="ticketFlag != null">
                ticket_flag = #{ticketFlag},
            </if>
            <if test="ticketStartAt != null">
                ticket_start_at = #{ticketStartAt},
            </if>
            <if test="ticketEndAt != null">
                ticket_end_at = #{ticketEndAt},
            </if>
            <if test="ticketAttach != null and ticketAttach != ''">
                ticket_attach = #{ticketAttach},
            </if>
            <if test="scoreFlag != null and scoreFlag != ''">
                score_flag = #{scoreFlag},
            </if>
            <if test="scoreStartAt != null">
                score_start_at = #{scoreStartAt},
            </if>
            <if test="scoreEndAt != null">
                score_end_at = #{scoreEndAt},
            </if>
            <if test="templateCopy != null and templateCopy != ''">
                template_copy = #{templateCopy},
            </if>
            <if test="enterpriseId != null and enterpriseId != ''">
                enterprise_id = #{enterpriseId},
            </if>
            <if test="createBy != null and createBy != ''">
                create_by = #{createBy},
            </if>
            <if test="createAt != null">
                create_at = #{createAt},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="updateAt != null">
                update_at = #{updateAt},
            </if>
        </set>
        where id = #{id}
    </update>
    <delete id="deleteById">
        delete
        from register_publish
        where id = #{id}
    </delete>
    <select id="queryByPageAndCondition" resultMap="RegisterPublishMap">
        select
        <include refid="sqlAllColumns"/>
        from register_publish
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="title != null and title != ''">
                and title = #{title}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="description != null and description != ''">
                and description = #{description}
            </if>
            <if test="startAt != null">
                and start_at = #{startAt}
            </if>
            <if test="endAt != null">
                and end_at = #{endAt}
            </if>
            <if test="ticketFlag != null">
                and ticket_flag = #{ticketFlag}
            </if>
            <if test="ticketStartAt != null">
                and ticket_start_at = #{ticketStartAt}
            </if>
            <if test="ticketEndAt != null">
                and ticket_end_at = #{ticketEndAt}
            </if>
            <if test="ticketAttach != null and ticketAttach != ''">
                and ticket_attach = #{ticketAttach}
            </if>
            <if test="scoreFlag != null and scoreFlag != ''">
                and score_flag = #{scoreFlag}
            </if>
            <if test="scoreStartAt != null">
                and score_start_at = #{scoreStartAt}
            </if>
            <if test="scoreEndAt != null">
                and score_end_at = #{scoreEndAt}
            </if>
            <if test="templateCopy != null and templateCopy != ''">
                and template_copy = #{templateCopy}
            </if>
            <if test="enterpriseId != null and enterpriseId != ''">
                and enterprise_id = #{enterpriseId}
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy}
            </if>
            <if test="createAt != null">
                and create_at = #{createAt}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy}
            </if>
            <if test="updateAt != null">
                and update_at = #{updateAt}
            </if>
        </where>
        ORDER BY
        <choose>
            <when test='sort == null or sort.trim == ""'>
                create_at DESC
            </when>
            <otherwise>
                `${sort}`
                <choose>
                    <when test='order != null and "DESC".equalsIgnoreCase(order.trim())'>
                        DESC
                    </when>
                    <otherwise>
                        ASC
                    </otherwise>
                </choose>
            </otherwise>
        </choose>
    </select>
    <select id="queryOneById" resultMap="RegisterPublishMap">
        select
        <include refid="sqlAllColumns"/>
        from register_publish
        where id = #{id}
    </select>
    <select id="queryOneByIdAndEnterpriseId" resultMap="RegisterPublishMap">
        select
        <include refid="sqlAllColumns"/>
        from register_publish
        where id = #{id} and enterprise_id = #{enterpriseId}
    </select>

</mapper>