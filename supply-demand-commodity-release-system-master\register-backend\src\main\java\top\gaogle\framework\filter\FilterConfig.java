package top.gaogle.framework.filter;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Filter配置
 */
@Configuration
public class FilterConfig {
//    @Value("${xss.excludes}")
//    private String excludes;
//
//    @Value("${xss.urlPatterns}")
//    private String urlPatterns;

//    @SuppressWarnings({"rawtypes", "unchecked"})
//    @Bean
//    @ConditionalOnProperty(value = "xss.enabled", havingValue = "true")
//    public FilterRegistrationBean xssFilterRegistration() {
//        FilterRegistrationBean registration = new FilterRegistrationBean();
//        registration.setDispatcherTypes(DispatcherType.REQUEST);
//        registration.setFilter(new XssFilter());
//        registration.addUrlPatterns(StringUtils.split(urlPatterns, ","));
//        registration.setName("xssFilter");
//        registration.setOrder(FilterRegistrationBean.HIGHEST_PRECEDENCE);
//        Map<String, String> initParameters = new HashMap<String, String>();
//        initParameters.put("excludes", excludes);
//        registration.setInitParameters(initParameters);
//        return registration;
//    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    @Bean
    public FilterRegistrationBean someFilterRegistration() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setFilter(new RepeatableFilter());
        registration.addUrlPatterns("/*");
        registration.setName("repeatableFilter");
        registration.setOrder(FilterRegistrationBean.LOWEST_PRECEDENCE);
        return registration;
    }

}
